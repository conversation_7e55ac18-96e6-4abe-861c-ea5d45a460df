<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/maps/henan.js"></script>

    

<style>
/* 指北针样式 - 与主页面一致 */
.compass-control {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    color: #333;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    z-index: 1000;
    transition: all 0.3s ease;
}

.compass-control:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

.compass-needle {
    position: relative;
    width: 30px;
    height: 30px;
}

.compass-needle::before {
    content: 'N';
    position: absolute;
    top: 2px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 16px;
    font-weight: bold;
    color: #dc3545;
}

.compass-needle::after {
    content: '↑';
    position: absolute;
    top: 18px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    font-weight: bold;
    color: #dc3545;
}

/* 比例尺样式 - 与主页面一致 */
.scale-control {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.2);
    border-radius: 2px;
    padding: 5px 8px;
    font-size: 11px;
    color: #333;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    min-width: 100px;
    font-family: 'Courier New', monospace;
}

.scale-text {
    text-align: center;
    font-weight: bold;
    margin: 2px 0;
}

.scale-line {
    height: 3px;
    background: #333;
    margin: 4px 0;
    position: relative;
}

.scale-line::before,
.scale-line::after {
    content: '';
    position: absolute;
    top: -2px;
    width: 1px;
    height: 7px;
    background: #333;
}

.scale-line::before {
    left: 0;
}

.scale-line::after {
    right: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .compass-control {
        width: 45px;
        height: 45px;
        top: 15px;
        right: 15px;
    }

    .compass-needle {
        width: 25px;
        height: 25px;
    }

    .compass-needle::before {
        font-size: 14px;
    }

    .compass-needle::after {
        font-size: 10px;
    }

    .scale-control {
        bottom: 15px;
        left: 15px;
        padding: 6px 10px;
        font-size: 11px;
        min-width: 100px;
    }
}
</style>

</head>
<body >
    <div id="39a5527c1edd4283b227d105464f382e" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_39a5527c1edd4283b227d105464f382e = echarts.init(
            document.getElementById('39a5527c1edd4283b227d105464f382e'), 'white', {renderer: 'canvas'});
        var option_39a5527c1edd4283b227d105464f382e = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "map",
            "name": "\u884c\u653f\u533a\u5212",
            "label": {
                "show": true,
                "color": "#2c3e50",
                "margin": 8,
                "fontSize": 10,
                "fontWeight": "bold",
                "valueAnimation": false
            },
            "map": "\u6cb3\u5357",
            "data": [
                {
                    "name": "\u90d1\u5dde\u5e02",
                    "value": 1
                },
                {
                    "name": "\u6d1b\u9633\u5e02",
                    "value": 1
                },
                {
                    "name": "\u5f00\u5c01\u5e02",
                    "value": 1
                },
                {
                    "name": "\u5357\u9633\u5e02",
                    "value": 1
                },
                {
                    "name": "\u5b89\u9633\u5e02",
                    "value": 1
                },
                {
                    "name": "\u5546\u4e18\u5e02",
                    "value": 1
                },
                {
                    "name": "\u65b0\u4e61\u5e02",
                    "value": 1
                },
                {
                    "name": "\u5e73\u9876\u5c71\u5e02",
                    "value": 1
                },
                {
                    "name": "\u8bb8\u660c\u5e02",
                    "value": 1
                },
                {
                    "name": "\u7126\u4f5c\u5e02",
                    "value": 1
                },
                {
                    "name": "\u5468\u53e3\u5e02",
                    "value": 1
                },
                {
                    "name": "\u4fe1\u9633\u5e02",
                    "value": 1
                },
                {
                    "name": "\u9a7b\u9a6c\u5e97\u5e02",
                    "value": 1
                },
                {
                    "name": "\u6fee\u9633\u5e02",
                    "value": 1
                },
                {
                    "name": "\u4e09\u95e8\u5ce1\u5e02",
                    "value": 1
                },
                {
                    "name": "\u6f2f\u6cb3\u5e02",
                    "value": 1
                },
                {
                    "name": "\u9e64\u58c1\u5e02",
                    "value": 1
                },
                {
                    "name": "\u6d4e\u6e90\u5e02",
                    "value": 1
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1.2,
            "zlevel": 0,
            "z": 2,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "mapValueCalculation": "sum",
            "showLegendSymbol": true,
            "itemStyle": {
                "color": "#a8e6cf",
                "borderColor": "#2c3e50",
                "borderWidth": 2
            },
            "emphasis": {},
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u884c\u653f\u533a\u5212"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": "{b}<br/>\u884c\u653f\u7ea7\u522b: \u5730\u7ea7\u5e02",
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u6cb3\u5357\u7701\u884c\u653f\u533a\u5212\u56fe",
            "target": "blank",
            "subtext": "\u5e02\u7ea7\u884c\u653f\u533a\u5212\u8fb9\u754c",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "color": "#2c3e50",
                "fontWeight": "bold",
                "fontSize": 18
            },
            "subtextStyle": {
                "color": "#7f8c8d",
                "fontSize": 12
            }
        }
    ],
    "visualMap": {
        "show": false,
        "type": "continuous",
        "min": 0,
        "max": 1,
        "inRange": {
            "color": [
                "#a8e6cf",
                "#a8e6cf"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "hoverLink": true,
        "orient": "vertical",
        "padding": 5,
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 140,
        "borderWidth": 0
    }
};
        chart_39a5527c1edd4283b227d105464f382e.setOption(option_39a5527c1edd4283b227d105464f382e);
    </script>

<!-- 指北针控件 -->
<div class="compass-control" id="compassControl" title="指北针 - 点击重置地图方向" onclick="resetMapOrientation()">
    <div class="compass-needle"></div>
</div>

<!-- 比例尺控件 -->
<div class="scale-control" id="scaleControl">
    <div class="scale-text" id="scaleText">1:1,200,000</div>
    <div class="scale-line" id="scaleLine" style="width: 80px;"></div>
    <div class="scale-text" id="scaleDistance">0 ——— 120km</div>
</div>

<script>
// 地图控件管理类 - 与交互地图保持一致
class StaticMapControls {
    constructor() {
        this.compassControl = document.getElementById('compassControl');
        this.scaleControl = document.getElementById('scaleControl');
        this.scaleText = document.getElementById('scaleText');
        this.scaleLine = document.getElementById('scaleLine');
        this.scaleDistance = document.getElementById('scaleDistance');
        this.currentAdjustment = 1.0;
        this.baseScaleInfo = {'ratio': '1:1,200,000', 'lineWidth': 80, 'distance': '0 ——— 120km'};
        this.init();
    }

    init() {
        // 绑定指北针点击事件
        if (this.compassControl) {
            this.compassControl.addEventListener('click', () => {
                this.resetMapOrientation();
            });
        }

        // 初始化比例尺
        this.updateScale();
        
        // 监听窗口大小变化，动态调整比例尺
        window.addEventListener('resize', () => {
            this.updateScaleOnResize();
        });

        // 使用ResizeObserver监听容器大小变化
        if (this.scaleControl && window.ResizeObserver) {
            const resizeObserver = new ResizeObserver(() => {
                this.updateScaleOnResize();
            });
            resizeObserver.observe(document.body);
        }
    }

    // 重置地图方向（指北针功能）
    resetMapOrientation() {
        // 对于静态地图，显示提示信息
        this.showAlert('地图已对准正北方向\n当前地图已按标准地理方向显示', 'info');
    }

    // 显示提示信息
    showAlert(message, type = 'info') {
        // 简单的提示实现
        alert(message);
    }

    // 更新比例尺信息
    updateScale() {
        if (!this.scaleControl) return;
        
        // 动态计算比例尺宽度
        const containerWidth = window.innerWidth;
        const baseScaleWidth = Math.min(120, Math.max(80, containerWidth * 0.15));
        const adjustedScaleWidth = Math.round(baseScaleWidth * this.currentAdjustment);
        
        if (this.scaleLine) {
            this.scaleLine.style.width = adjustedScaleWidth + 'px';
        }
        
        console.log('比例尺已更新，宽度:', adjustedScaleWidth + 'px');
    }

    // 窗口大小变化时更新比例尺
    updateScaleOnResize() {
        this.updateScale();
    }

    // 显示控件
    show() {
        if (this.compassControl) {
            this.compassControl.style.display = 'flex';
        }
        if (this.scaleControl) {
            this.scaleControl.style.display = 'block';
        }
    }

    // 隐藏控件
    hide() {
        if (this.compassControl) {
            this.compassControl.style.display = 'none';
        }
        if (this.scaleControl) {
            this.scaleControl.style.display = 'none';
        }
    }
}

// 全局地图控件实例
let staticMapControls;

// 重置地图方向功能（向后兼容）
function resetMapOrientation() {
    if (staticMapControls) {
        staticMapControls.resetMapOrientation();
    } else {
        alert('地图已对准正北方向\n当前地图已按标准地理方向显示');
    }
}

// 动态更新比例尺（向后兼容）
function updateMapScale() {
    if (staticMapControls) {
        staticMapControls.updateScale();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🗺️ 静态地图导航控件初始化中...');
    
    // 初始化地图控件
    staticMapControls = new StaticMapControls();
    
    // 显示控件
    staticMapControls.show();
    
    console.log('✅ 静态地图导航控件已加载，支持动态缩放');
});
</script>

</body>
</html>
