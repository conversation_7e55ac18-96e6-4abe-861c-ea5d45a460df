<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .layer-switch-btn {
            width: 100px;
            height: 60px;
            border-radius: 8px;
            border: 2px solid;
            background: rgba(255, 255, 255, 0.95);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
            margin: 5px;
        }

        .layer-switch-btn i {
            font-size: 18px;
            margin-bottom: 4px;
        }

        .layer-switch-btn .btn-text {
            font-size: 10px;
            line-height: 1;
            white-space: nowrap;
        }

        .layer-switch-btn:hover {
            transform: translateX(-5px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
        }

        .layer-switch-btn.active {
            background: rgba(13, 110, 253, 0.95);
            color: white;
            border-color: #0d6efd;
            transform: translateX(-5px);
            box-shadow: 0 4px 12px rgba(13, 110, 253, 0.4);
        }

        .layer-switch-btn[data-layer="interactive"] {
            border-color: #0d6efd;
            color: #0d6efd;
        }

        .layer-switch-btn[data-layer="risk"] {
            border-color: #dc3545;
            color: #dc3545;
        }

        .layer-switch-btn[data-layer="landuse"] {
            border-color: #198754;
            color: #198754;
        }

        .layer-switch-btn[data-layer="admin"] {
            border-color: #0dcaf0;
            color: #0dcaf0;
        }

        .test-area {
            min-height: 400px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #6c757d;
            margin: 20px 0;
        }

        .log-area {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>图层切换按钮测试</h1>
        
        <div class="row">
            <div class="col-md-8">
                <div class="test-area" id="testArea">
                    点击右侧按钮测试功能
                </div>
                
                <div class="log-area" id="logArea">
                    <div>控制台日志:</div>
                </div>
            </div>
            
            <div class="col-md-4">
                <h5>图层切换按钮</h5>
                <div class="d-flex flex-column gap-2">
                    <!-- 交互地图按钮 -->
                    <button class="btn btn-primary layer-switch-btn active"
                            data-layer="interactive"
                            title="交互地图"
                            onclick="switchToLayer('interactive')">
                        <i class="fas fa-layer-group"></i>
                        <span class="btn-text">交互地图</span>
                    </button>

                    <!-- 风险区划图按钮 -->
                    <button class="btn btn-outline-danger layer-switch-btn"
                            data-layer="risk"
                            title="风险区划图"
                            onclick="switchToLayer('risk')">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span class="btn-text">风险区划</span>
                    </button>

                    <!-- 土地利用图按钮 -->
                    <button class="btn btn-outline-success layer-switch-btn"
                            data-layer="landuse"
                            title="土地利用图"
                            onclick="switchToLayer('landuse')">
                        <i class="fas fa-seedling"></i>
                        <span class="btn-text">土地利用</span>
                    </button>

                    <!-- 行政区划图按钮 -->
                    <button class="btn btn-outline-info layer-switch-btn"
                            data-layer="admin"
                            title="行政区划图"
                            onclick="switchToLayer('admin')">
                        <i class="fas fa-map-marked-alt"></i>
                        <span class="btn-text">行政区划</span>
                    </button>
                </div>
                
                <div class="mt-3">
                    <h6>测试按钮</h6>
                    <button class="btn btn-secondary btn-sm" onclick="testFunction()">测试函数</button>
                    <button class="btn btn-warning btn-sm" onclick="clearLog()">清除日志</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // 日志函数
        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div>[${time}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        // 图层切换函数
        function switchToLayer(layerType) {
            log(`🔄 切换到图层: ${layerType}`);
            
            try {
                // 更新按钮状态
                document.querySelectorAll('.layer-switch-btn').forEach(btn => {
                    btn.classList.remove('active');
                });

                const targetBtn = document.querySelector(`[data-layer="${layerType}"]`);
                if (targetBtn) {
                    targetBtn.classList.add('active');
                    log(`✅ 按钮状态已更新: ${layerType}`);
                } else {
                    log(`❌ 找不到图层按钮: ${layerType}`);
                }

                // 更新测试区域显示
                const testArea = document.getElementById('testArea');
                const layerNames = {
                    'interactive': '交互地图',
                    'risk': '风险区划图',
                    'landuse': '土地利用图',
                    'admin': '行政区划图'
                };
                
                testArea.innerHTML = `当前图层: ${layerNames[layerType] || layerType}`;
                testArea.style.background = getLayerColor(layerType);
                
                log(`✅ 图层切换完成: ${layerType}`);

            } catch (error) {
                log(`❌ 图层切换失败: ${error.message}`);
                console.error('图层切换失败:', error);
            }
        }

        // 获取图层颜色
        function getLayerColor(layerType) {
            const colors = {
                'interactive': '#e3f2fd',
                'risk': '#ffebee',
                'landuse': '#e8f5e8',
                'admin': '#e0f7fa'
            };
            return colors[layerType] || '#f8f9fa';
        }

        // 测试函数
        function testFunction() {
            log('🧪 测试函数被调用');
            log(`switchToLayer函数状态: ${typeof switchToLayer}`);
            log(`jQuery状态: ${typeof $ !== 'undefined' ? '已加载' : '未加载'}`);
            log(`Bootstrap状态: ${typeof bootstrap !== 'undefined' ? '已加载' : '未加载'}`);
        }

        // 清除日志
        function clearLog() {
            document.getElementById('logArea').innerHTML = '<div>控制台日志:</div>';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('✅ 页面加载完成');
            log('🔧 switchToLayer函数已准备就绪');
            
            // 测试函数是否可用
            if (typeof switchToLayer === 'function') {
                log('✅ switchToLayer函数定义正确');
            } else {
                log('❌ switchToLayer函数未定义');
            }
        });

        // 确保函数在全局作用域中可用
        window.switchToLayer = switchToLayer;
    </script>
</body>
</html>