<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>比例尺调试测试</title>
    <script src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
    <style>
        .map-control-scale {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            font-family: Arial, sans-serif;
            font-size: 12px;
            z-index: 1000;
        }

        .scale-bar {
            height: 4px;
            background: #333;
            margin-bottom: 4px;
            border-radius: 2px;
            width: 100px;
        }

        .scale-info {
            color: #666;
            text-align: center;
        }
        
        #debug {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-width: 300px;
            z-index: 1001;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div id="main" style="width: 900px; height: 500px;"></div>
    
    <div class="map-control-scale" title="比例尺">
        <div class="scale-bar" id="scaleBar">1000 km</div>
        <div class="scale-info" id="scaleInfo">缩放级别: 1</div>
    </div>
    
    <div id="debug">
        <div>调试信息:</div>
        <div id="debugInfo">初始化中...</div>
    </div>

    <script>
        var chartDom = document.getElementById('main');
        var myChart = echarts.init(chartDom);
        var debugInfo = document.getElementById('debugInfo');
        
        function log(msg) {
            console.log(msg);
            debugInfo.innerHTML += '<br>' + new Date().toLocaleTimeString() + ': ' + msg;
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }

        log('开始初始化ECharts地图...');

        // 检查ECharts是否加载成功
        if (typeof echarts === 'undefined') {
            log('错误: ECharts未加载');
        } else {
            log('ECharts已加载，版本: ' + echarts.version);
        }

        // 使用中国地图而不是河南省地图，避免地图数据加载问题
        var option = {
            geo: {
                map: 'china',
                zoom: 1,
                roam: true,
                itemStyle: {
                    areaColor: '#e7e8ea'
                },
                emphasis: {
                    itemStyle: {
                        areaColor: '#389BB7'
                    }
                }
            },
            series: [{
                type: 'map',
                map: 'china',
                data: []
            }]
        };

        try {
            myChart.setOption(option);
            log('地图配置已设置（使用中国地图）');
        } catch (error) {
            log('设置地图配置时出错: ' + error.message);
        }
        
        // 简化的比例尺计算
        function calculateSimpleScale(mapType, zoom, chartDom) {
            var containerWidth = chartDom.offsetWidth;
            var containerHeight = chartDom.offsetHeight;

            log('容器尺寸: ' + containerWidth + 'x' + containerHeight);

            // 根据地图类型选择地理范围
            var bounds;
            if (mapType === '河南') {
                bounds = { north: 36.4, south: 31.4, east: 116.6, west: 110.4 };
            } else {
                // 中国的地理范围
                bounds = { north: 53.5, south: 18.2, east: 134.8, west: 73.5 };
            }
            
            var geoWidth = Math.abs(bounds.east - bounds.west);
            var geoHeight = Math.abs(bounds.north - bounds.south);
            var avgLat = (bounds.north + bounds.south) / 2;
            var latFactor = Math.cos(avgLat * Math.PI / 180);
            
            var realWidthKm = geoWidth * 111 * latFactor;
            var realHeightKm = geoHeight * 111;
            var realDistanceKm = Math.min(realWidthKm, realHeightKm);
            var pixelDistance = Math.min(containerWidth, containerHeight);
            
            var kmPerPixel = realDistanceKm / (pixelDistance * zoom);
            var scalePixels = 100;
            var scaleKm = kmPerPixel * scalePixels;
            
            var displayKm, scaleText;
            if (scaleKm >= 100) {
                displayKm = Math.round(scaleKm / 100) * 100;
                scaleText = displayKm + ' km';
            } else if (scaleKm >= 10) {
                displayKm = Math.round(scaleKm / 10) * 10;
                scaleText = displayKm + ' km';
            } else {
                displayKm = Math.round(scaleKm);
                scaleText = displayKm + ' km';
            }
            
            var infoText = '1:' + Math.round(displayKm * 100000) + ' (缩放: ' + zoom.toFixed(1) + 'x)';
            
            return {
                scaleText: scaleText,
                infoText: infoText,
                pixelWidth: scalePixels
            };
        }
        
        function updateScale() {
            try {
                var scaleBar = document.getElementById('scaleBar');
                var scaleInfo = document.getElementById('scaleInfo');
                
                if (!scaleBar || !scaleInfo) {
                    log('比例尺元素未找到');
                    return;
                }
                
                var option = myChart.getOption();
                if (!option) {
                    log('无法获取地图配置');
                    return;
                }
                
                var geo = option.geo && option.geo[0];
                if (!geo) {
                    log('地理配置未找到');
                    return;
                }
                
                var zoom = geo.zoom || 1;
                var mapType = geo.map || 'china';
                
                log('当前缩放级别: ' + zoom + ', 地图类型: ' + mapType);
                
                var scaleData = calculateSimpleScale(mapType, zoom, chartDom);
                
                scaleBar.textContent = scaleData.scaleText;
                scaleInfo.textContent = scaleData.infoText;
                
                log('比例尺已更新: ' + scaleData.scaleText + ' | ' + scaleData.infoText);
                
            } catch (error) {
                log('更新比例尺时出错: ' + error.message);
            }
        }
        
        // 监听事件
        myChart.on('georoam', function(params) {
            log('地图漫游事件: zoom=' + (params.zoom || 'undefined'));
            setTimeout(updateScale, 100);
        });
        
        chartDom.addEventListener('wheel', function(e) {
            log('鼠标滚轮事件');
            setTimeout(updateScale, 100);
        });
        
        // 初始化
        setTimeout(function() {
            log('开始初始化比例尺...');
            updateScale();
        }, 1000);
        
        setInterval(function() {
            log('定期更新比例尺...');
            updateScale();
        }, 3000);
        
        log('测试页面初始化完成');
    </script>
</body>
</html>
