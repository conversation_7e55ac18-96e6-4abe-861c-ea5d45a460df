"""
PyEcharts地图生成器
用于生成基于风险评估数据的省份风险区划地图
"""
import os
import tempfile
import time
import re
from typing import Dict, List, Optional
from flask import current_app


class PyEchartsMapGenerator:
    """PyEcharts地图生成器"""
    
    def __init__(self):
        """初始化地图生成器"""
        # 风险等级颜色映射（与右侧图例保持一致，包含五个风险等级）
        self.risk_colors = {
            '未评估': '#e9ecef',      # 浅灰色
            '极低风险': '#d4edda',    # 浅绿色（与右侧图例一致）
            '低风险': '#28a745',      # 绿色
            '中风险': '#ffc107',      # 黄色
            '高风险': '#fd7e14',      # 橙色
            '极高风险': '#dc3545'     # 红色
        }
        
        # 风险等级数值映射（用于PyEcharts显示，包含五个风险等级）
        self.risk_values = {
            '未评估': 0,
            '极低风险': 1,
            '低风险': 2,
            '中风险': 3,
            '高风险': 4,
            '极高风险': 5
        }
        
        # 省份名称映射（PyEcharts地图名称）
        self.province_map_names = {
            '河南省': '河南',
            '湖北省': '湖北',
            '湖南省': '湖南',
            '广东省': '广东',
            '广西壮族自治区': '广西',
            '江苏省': '江苏',
            '浙江省': '浙江',
            '安徽省': '安徽',
            '福建省': '福建',
            '江西省': '江西',
            '山东省': '山东',
            '河北省': '河北',
            '山西省': '山西',
            '陕西省': '陕西',
            '甘肃省': '甘肃',
            '青海省': '青海',
            '四川省': '四川',
            '贵州省': '贵州',
            '云南省': '云南',
            '辽宁省': '辽宁',
            '吉林省': '吉林',
            '黑龙江省': '黑龙江',
            '内蒙古自治区': '内蒙古',
            '新疆维吾尔自治区': '新疆',
            '西藏自治区': '西藏',
            '宁夏回族自治区': '宁夏',
            '海南省': '海南',
            '台湾省': '台湾',
            '香港特别行政区': '香港',
            '澳门特别行政区': '澳门',
            # 直辖市
            '北京市': '北京',
            '天津市': '天津',
            '上海市': '上海',
            '重庆市': '重庆'
        }

        # 土地利用类型颜色映射
        self.landuse_colors = {
            '耕地': '#FFD700',      # 金色
            '林地': '#228B22',      # 森林绿
            '草地': '#90EE90',      # 浅绿色
            '水域': '#4169E1',      # 皇家蓝
            '建设用地': '#808080',   # 灰色
            '未利用地': '#D2B48C',   # 棕褐色
            '住宅用地': '#FFB6C1',   # 浅粉色
            '商业用地': '#FF6347',   # 番茄红
            '工业用地': '#8B4513',   # 马鞍棕
            '交通用地': '#696969',   # 暗灰色
            '其他': '#DDA0DD'       # 梅花色
        }

        # 土地利用类型数值映射（用于PyEcharts显示）
        self.landuse_values = {
            '耕地': 1,
            '林地': 2,
            '草地': 3,
            '水域': 4,
            '建设用地': 5,
            '未利用地': 6,
            '住宅用地': 7,
            '商业用地': 8,
            '工业用地': 9,
            '交通用地': 10,
            '其他': 0
        }

        # 省份内主要城市映射（用于行政区划地图）
        self.province_cities = {
            '河南': ['郑州市', '洛阳市', '开封市', '南阳市', '安阳市', '商丘市', '新乡市', '平顶山市',
                   '许昌市', '焦作市', '周口市', '信阳市', '驻马店市', '濮阳市', '三门峡市', '漯河市', '鹤壁市', '济源市'],
            '湖北': ['武汉市', '黄石市', '十堰市', '宜昌市', '襄阳市', '鄂州市', '荆门市', '孝感市',
                   '荆州市', '神农架林区','天门市','仙桃市','潜江市','黄冈市', '咸宁市', '随州市', '恩施土家族苗族自治州'],
            '湖南': ['长沙市', '株洲市', '湘潭市', '衡阳市', '邵阳市', '岳阳市', '常德市', '张家界市',
                   '益阳市', '郴州市', '永州市', '怀化市', '娄底市', '湘西土家族苗族自治州'],
            '广东': ['广州市', '深圳市', '珠海市', '汕头市', '佛山市', '韶关市', '湛江市', '肇庆市',
                   '江门市', '茂名市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市',
                   '东莞市', '中山市', '潮州市', '揭阳市', '云浮市'],
            '江苏': ['南京市', '无锡市', '徐州市', '常州市', '苏州市', '南通市', '连云港市', '淮安市',
                   '盐城市', '扬州市', '镇江市', '泰州市', '宿迁市'],
            '浙江': ['杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '衢州市',
                   '舟山市', '台州市', '丽水市'],
            '山东': ['济南市', '青岛市', '淄博市', '枣庄市', '东营市', '烟台市', '潍坊市', '济宁市',
                   '泰安市', '威海市', '日照市', '临沂市', '德州市', '聊城市', '滨州市', '菏泽市'],
            '四川': ['成都市', '自贡市', '攀枝花市', '泸州市', '德阳市', '绵阳市', '广元市', '遂宁市',
                   '内江市', '乐山市', '南充市', '眉山市', '宜宾市', '广安市', '达州市', '雅安市',
                   '巴中市', '资阳市', '阿坝藏族羌族自治州', '甘孜藏族自治州', '凉山彝族自治州'],
            '福建': ['福州市', '厦门市', '莆田市', '三明市', '泉州市', '漳州市', '南平市', '龙岩市', '宁德市'],

            # 直辖市
            '北京': ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区',
                   '通州区', '顺义区', '大兴区', '昌平区', '平谷区', '怀柔区', '密云区', '延庆区'],
            '天津': ['和平区', '河东区', '河西区', '南开区', '河北区', '红桥区', '东丽区', '西青区',
                   '津南区', '北辰区', '武清区', '宝坻区', '滨海新区', '宁河区', '静海区', '蓟州区'],
            '上海': ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '闵行区',
                   '宝山区', '嘉定区', '浦东新区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区'],
            '重庆': ['万州区', '涪陵区', '渝中区', '大渡口区', '江北区', '沙坪坝区', '九龙坡区', '南岸区',
                   '北碚区', '綦江区', '大足区', '渝北区', '巴南区', '黔江区', '长寿区', '江津区', '合川区',
                   '永川区', '南川区', '璧山区', '铜梁区', '潼南区', '荣昌区', '开州区', '梁平县', '武隆县',
                   '城口县', '丰都县', '垫江县', '忠县', '云阳县', '奉节县', '巫山县', '巫溪县', '石柱土家族自治县',
                   '秀山土家族苗族自治县', '酉阳土家族苗族自治县', '彭水苗族土家族自治县'],

            # 其他省份
            '河北': ['石家庄市', '唐山市', '秦皇岛市', '邯郸市', '邢台市', '保定市', '张家口市', '承德市',
                   '沧州市', '廊坊市', '衡水市'],
            '山西': ['太原市', '大同市', '阳泉市', '长治市', '晋城市', '朔州市', '晋中市', '运城市',
                   '忻州市', '临汾市', '吕梁市'],
            '辽宁': ['沈阳市', '大连市', '鞍山市', '抚顺市', '本溪市', '丹东市', '锦州市', '营口市',
                   '阜新市', '辽阳市', '盘锦市', '铁岭市', '朝阳市', '葫芦岛市'],
            '吉林': ['长春市', '吉林市', '四平市', '辽源市', '通化市', '白山市', '松原市', '白城市',
                   '延边朝鲜族自治州'],
            '黑龙江': ['哈尔滨市', '齐齐哈尔市', '鸡西市', '鹤岗市', '双鸭山市', '大庆市', '伊春市', '佳木斯市',
                     '七台河市', '牡丹江市', '黑河市', '绥化市', '大兴安岭地区'],
            '安徽': ['合肥市', '芜湖市', '蚌埠市', '淮南市', '马鞍山市', '淮北市', '铜陵市', '安庆市',
                   '黄山市', '滁州市', '阜阳市', '宿州市', '六安市', '亳州市', '池州市', '宣城市'],
            '江西': ['南昌市', '景德镇市', '萍乡市', '九江市', '新余市', '鹰潭市', '赣州市', '吉安市',
                   '宜春市', '抚州市', '上饶市'],
            '贵州': ['贵阳市', '六盘水市', '遵义市', '安顺市', '毕节市', '铜仁市', 
            '黔西南布依族苗族自治州', '黔东南苗族侗族自治州', '黔南布依族苗族自治州'],
            '云南': ['昆明市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市', '普洱市', '临沧市',
                    '楚雄彝族自治州', '红河哈尼族彝族自治州', '文山壮族苗族自治州', '西双版纳傣族自治州',
                    '大理白族自治州', '德宏傣族景颇族自治州', '怒江傈僳族自治州', '迪庆藏族自治州'],
            '陕西': ['西安市', '铜川市', '宝鸡市', '咸阳市', '渭南市', '延安市', '汉中市', '榆林市', '安康市', '商洛市'],
            '甘肃': ['兰州市', '嘉峪关市', '金昌市', '白银市', '天水市', '武威市', '张掖市', '平凉市', '酒泉市', 
                    '庆阳市', '定西市', '陇南市', '临夏回族自治州', '甘南藏族自治州'],
            '青海': ['西宁市', '海东市', '海北藏族自治州', '黄南藏族自治州', '海南藏族自治州', 
                    '果洛藏族自治州', '玉树藏族自治州', '海西蒙古族藏族自治州'],
            '海南': ['海口市', '三亚市', '三沙市', '儋州市', '五指山市', '琼海市', '文昌市', '万宁市', 
                    '东方市', '定安县', '屯昌县', '澄迈县', '临高县', '白沙黎族自治县', '昌江黎族自治县', 
                    '乐东黎族自治县', '陵水黎族自治县', '保亭黎族苗族自治县', '琼中黎族苗族自治县'],
            '台湾': ['台北市', '新北市', '桃园市', '台中市', '台南市', '高雄市', '基隆市', '新竹市', 
                    '嘉义市', '新竹县', '苗栗县', '彰化县', '南投县', '云林县', '嘉义县', '屏东县', 
                    '宜兰县', '花莲县', '台东县', '澎湖县'],
    
            # 自治区
            '广西': ['南宁市', '柳州市', '桂林市', '梧州市', '北海市', '防城港市', '钦州市', '贵港市', 
                    '玉林市', '百色市', '贺州市', '河池市', '来宾市', '崇左市'],
            '内蒙古': ['呼和浩特市', '包头市', '乌海市', '赤峰市', '通辽市', '鄂尔多斯市', '呼伦贝尔市', 
                     '巴彦淖尔市', '乌兰察布市', '兴安盟', '锡林郭勒盟', '阿拉善盟'],
            '西藏': ['拉萨市', '日喀则市', '昌都市', '林芝市', '山南市', '那曲市', '阿里地区'],
            '宁夏': ['银川市', '石嘴山市', '吴忠市', '固原市', '中卫市'],
            '新疆': ['乌鲁木齐市', '克拉玛依市', '吐鲁番市', '哈密市', '昌吉回族自治州', '博尔塔拉蒙古自治州', 
                    '巴音郭楞蒙古自治州', '阿克苏地区', '克孜勒苏柯尔克孜自治州', '喀什地区', '和田地区',
                    '伊犁哈萨克自治州', '塔城地区', '阿勒泰地区'],
    
            # 特别行政区
            '香港': ['香港岛', '九龙', '新界'],
            '澳门': ['澳门半岛', '氹仔岛', '路环岛', '路氹城']    
        }

    def get_risk_level_from_index(self, risk_index: float) -> str:
        """
        根据风险指数确定风险等级
        支持五个风险等级：极低风险、低风险、中风险、高风险、极高风险
        """
        if risk_index is None:
            return '未评估'

        try:
            risk_index = float(risk_index)
        except (ValueError, TypeError):
            return '未评估'

        # 五级风险等级划分
        if risk_index <= 0.2:
            return '极低风险'
        elif risk_index <= 0.4:
            return '低风险'
        elif risk_index <= 0.6:
            return '中风险'
        elif risk_index <= 0.8:
            return '高风险'
        else:
            return '极高风险'

    def add_map_controls_to_html(self, file_path: str) -> None:
        """
        为PyEcharts生成的HTML文件添加指北针和比例尺控件

        Args:
            file_path: HTML文件路径
        """
        try:
            # 读取HTML文件
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()

            # 指北针和比例尺的CSS样式（与交互地图保持一致）
            controls_css = """
<style>
/* 地图控件样式 - 指北针和比例尺 */
.map-control-compass {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    padding: 8px;
    transition: all 0.3s ease;
    cursor: default;
    user-select: none;
    z-index: 1000;
}

.map-control-compass:hover {
    background: rgba(255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
}

.compass-container {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.compass-needle {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.compass-north {
    font-size: 12px;
    font-weight: bold;
    color: #dc3545;
    margin-bottom: 2px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.compass-arrow {
    font-size: 20px;
    color: #dc3545;
    line-height: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.map-control-scale {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    padding: 6px 10px;
    transition: all 0.3s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    z-index: 1000;
}

.map-control-scale:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.scale-bar {
    border: 2px solid #333;
    border-top: none;
    color: #333;
    font-size: 11px;
    font-weight: 600;
    line-height: 1.2;
    padding: 2px 5px 1px;
    background: transparent;
    white-space: nowrap;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    margin-bottom: 4px;
    min-width: 60px;
    max-width: 200px;
    text-align: center;
    transition: width 0.3s ease;
    position: relative;
}

.scale-info {
    font-size: 10px;
    color: #666;
    text-align: center;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}
</style>
"""

            # 指北针和比例尺的HTML结构
            controls_html = """
<div class="map-control-compass" title="指北针 - 北向指示器">
    <div class="compass-container">
        <div class="compass-needle">
            <div class="compass-north">N</div>
            <div class="compass-arrow">↑</div>
        </div>
    </div>
</div>

<div class="map-control-scale" title="比例尺">
    <div class="scale-bar" id="scaleBar">1000 km</div>
    <div class="scale-info" id="scaleInfo">缩放级别: 1</div>
</div>
"""

            # 动态比例尺的JavaScript代码
            scale_js = """
<script>
// 动态比例尺功能
function updateScale() {
    try {
        // 获取ECharts实例
        var chartDom = document.getElementById('main');
        if (!chartDom) return;

        var myChart = echarts.getInstanceByDom(chartDom);
        if (!myChart) return;

        // 获取当前缩放级别和地图配置
        var option = myChart.getOption();
        var geo = option.geo && option.geo[0];
        if (!geo) return;

        var zoom = geo.zoom || 1;
        var scaleBar = document.getElementById('scaleBar');
        var scaleInfo = document.getElementById('scaleInfo');

        if (scaleBar && scaleInfo) {
            // 获取地图类型和地理范围
            var mapType = geo.map || 'china';
            var scaleData = calculateRealScale(mapType, zoom, chartDom);

            scaleBar.textContent = scaleData.scaleText;
            scaleInfo.textContent = '缩放: ' + zoom.toFixed(1) + 'x';

            // 动态调整比例尺条的宽度
            var scaleWidth = Math.max(60, Math.min(150, scaleData.pixelWidth));
            scaleBar.style.width = scaleWidth + 'px';
        }
    } catch (error) {
        console.log('更新比例尺时出错:', error);
    }
}

// 计算真实比例尺
function calculateRealScale(mapType, zoom, chartDom) {
    try {
        // 获取图表容器的像素尺寸
        var containerWidth = chartDom.offsetWidth;
        var containerHeight = chartDom.offsetHeight;

        // 不同地图类型的地理范围（经纬度）
        var mapBounds = getMapBounds(mapType);
        if (!mapBounds) {
            return { scaleText: '100 km', pixelWidth: 100 };
        }

        // 计算地理宽度（经度差转换为公里）
        var lonDiff = mapBounds.east - mapBounds.west;
        var latDiff = mapBounds.north - mapBounds.south;

        // 使用地图中心纬度计算经度的实际距离
        var centerLat = (mapBounds.north + mapBounds.south) / 2;
        var latFactor = Math.cos(centerLat * Math.PI / 180);

        // 1度经度约等于111公里，考虑纬度影响
        var realWidthKm = lonDiff * 111 * latFactor;
        var realHeightKm = latDiff * 111;

        // 考虑缩放级别的影响
        var effectiveWidthKm = realWidthKm / zoom;
        var effectiveHeightKm = realHeightKm / zoom;

        // 计算每像素代表的公里数（使用较小的维度确保准确性）
        var kmPerPixel = Math.min(effectiveWidthKm / containerWidth, effectiveHeightKm / containerHeight);

        // 生成合适的比例尺长度（目标100像素）
        var targetPixels = 100;
        var targetKm = kmPerPixel * targetPixels;

        // 调整到合适的数值
        var scaleInfo = formatScaleValue(targetKm);
        var actualPixels = scaleInfo.value / kmPerPixel;

        return {
            scaleText: scaleInfo.text,
            pixelWidth: Math.round(actualPixels),
            kmPerPixel: kmPerPixel
        };

    } catch (error) {
        console.log('计算比例尺时出错:', error);
        return { scaleText: '100 km', pixelWidth: 100 };
    }
}

// 获取不同地图类型的地理边界
function getMapBounds(mapType) {
    var bounds = {
        'china': { north: 53.5, south: 18.2, east: 134.8, west: 73.5 },
        '河南': { north: 36.4, south: 31.4, east: 116.6, west: 110.4 },
        '北京': { north: 41.1, south: 39.4, east: 117.5, west: 115.4 },
        '上海': { north: 31.9, south: 30.7, east: 122.2, west: 120.9 },
        '广东': { north: 25.3, south: 20.2, east: 117.2, west: 109.7 },
        '江苏': { north: 35.1, south: 30.8, east: 121.9, west: 116.4 },
        '浙江': { north: 31.2, south: 27.0, east: 123.2, west: 118.0 },
        '山东': { north: 38.4, south: 34.4, east: 122.7, west: 114.8 },
        '四川': { north: 34.3, south: 26.0, east: 108.5, west: 97.4 },
        '湖北': { north: 33.3, south: 29.0, east: 116.1, west: 108.3 },
        '湖南': { north: 30.1, south: 24.6, east: 114.3, west: 108.8 },
        '福建': { north: 28.3, south: 23.5, east: 120.7, west: 115.8 },
        '安徽': { north: 34.7, south: 29.4, east: 119.3, west: 114.9 },
        '江西': { north: 30.0, south: 24.5, east: 118.5, west: 113.6 },
        '陕西': { north: 39.6, south: 31.7, east: 111.3, west: 105.5 }
    };

    // 移除省字后缀进行匹配
    var normalizedMapType = mapType.replace('省', '');
    return bounds[normalizedMapType] || bounds[mapType] || bounds['china'];
}

// 格式化比例尺数值
function formatScaleValue(km) {
    if (km >= 1000) {
        var thousands = Math.round(km / 1000);
        if (thousands >= 10) {
            thousands = Math.round(thousands / 10) * 10;
        }
        return { value: thousands * 1000, text: thousands + '000 km' };
    } else if (km >= 100) {
        var hundreds = Math.round(km / 100) * 100;
        return { value: hundreds, text: hundreds + ' km' };
    } else if (km >= 10) {
        var tens = Math.round(km / 10) * 10;
        return { value: tens, text: tens + ' km' };
    } else if (km >= 1) {
        var ones = Math.round(km);
        return { value: ones, text: ones + ' km' };
    } else {
        var meters = Math.round(km * 1000);
        if (meters >= 100) {
            meters = Math.round(meters / 100) * 100;
        } else if (meters >= 10) {
            meters = Math.round(meters / 10) * 10;
        }
        return { value: meters / 1000, text: meters + ' m' };
    }
}

// 监听地图缩放事件
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保ECharts完全加载
    setTimeout(function() {
        initializeScaleControls();
    }, 1500);

    // 如果页面已经加载完成，也尝试初始化
    if (document.readyState === 'complete') {
        setTimeout(initializeScaleControls, 500);
    }
});

// 初始化比例尺控件
function initializeScaleControls() {
    try {
        var chartDom = document.getElementById('main');
        if (!chartDom) {
            console.log('地图容器未找到，稍后重试...');
            setTimeout(initializeScaleControls, 1000);
            return;
        }

        var myChart = echarts.getInstanceByDom(chartDom);
        if (!myChart) {
            console.log('ECharts实例未找到，稍后重试...');
            setTimeout(initializeScaleControls, 1000);
            return;
        }

        console.log('开始初始化地图比例尺控件...');

        // 监听地图漫游事件（包括缩放和平移）
        myChart.on('georoam', function(params) {
            console.log('地图漫游事件:', params);
            // 使用防抖，避免频繁更新
            clearTimeout(window.scaleUpdateTimer);
            window.scaleUpdateTimer = setTimeout(function() {
                updateScale();
            }, 150);
        });

        // 监听地图完成事件
        myChart.on('finished', function() {
            console.log('地图渲染完成，更新比例尺');
            setTimeout(updateScale, 200);
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            clearTimeout(window.resizeTimer);
            window.resizeTimer = setTimeout(function() {
                console.log('窗口大小变化，更新比例尺');
                updateScale();
            }, 300);
        });

        // 初始化比例尺显示
        setTimeout(function() {
            updateScale();
            console.log('比例尺控件初始化完成');
        }, 500);

    } catch (error) {
        console.log('初始化比例尺监听器时出错:', error);
        // 如果初始化失败，稍后重试
        setTimeout(initializeScaleControls, 2000);
    }
}

// 手动触发比例尺更新（供外部调用）
window.updateMapScale = function() {
    updateScale();
};
</script>
"""

            # 在HTML中插入CSS样式
            if '<head>' in html_content:
                html_content = html_content.replace('<head>', '<head>' + controls_css)
            else:
                # 如果没有head标签，在html标签后插入
                html_content = html_content.replace('<html>', '<html><head>' + controls_css + '</head>')

            # 在body结束标签前插入控件HTML和JavaScript
            if '</body>' in html_content:
                html_content = html_content.replace('</body>', controls_html + scale_js + '</body>')
            else:
                # 如果没有body标签，在html结束标签前插入
                html_content = html_content.replace('</html>', controls_html + scale_js + '</html>')

            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            print(f"已为地图文件添加指北针和比例尺控件: {file_path}")

        except Exception as e:
            print(f"添加地图控件失败: {e}")
            import traceback
            traceback.print_exc()

    def generate_province_risk_map_custom(self, province_name: str, city_risk_data: List[Dict], year: int = None) -> Optional[str]:
        """
        生成省份风险区划地图
        
        Args:
            province_name: 省份名称
            city_risk_data: 城市风险数据列表
            year: 年份
            
        Returns:
            地图文件的相对路径
        """
        try:
            from pyecharts.charts import Map
            from pyecharts import options as opts
            from pyecharts.globals import ThemeType
            from pyecharts.globals import CurrentConfig
            
            # 配置PyEcharts使用CDN资源
            CurrentConfig.ONLINE_HOST = "https://assets.pyecharts.org/assets/v5/"

            # 标准化省份名称
            map_name = self.province_map_names.get(province_name, province_name.replace('省', ''))
            
            # 准备数据
            map_data = []
            for city_data in city_risk_data:
                city_name = city_data['city_name']
                risk_level = city_data['risk_level']
                risk_value = self.risk_values.get(risk_level, 0)
                
                # 标准化城市名称
                if not city_name.endswith(('市', '县', '区')):
                    city_name += '市'
                
                map_data.append([city_name, risk_value])
            
            # 创建地图
            map_chart = (
                Map(init_opts=opts.InitOpts(
                    width="900px", 
                    height="500px",
                    theme=ThemeType.WHITE
                ))
                .add(
                    series_name="风险等级",
                    data_pair=map_data,
                    maptype=map_name,
                    is_roam=True,
                    zoom=1.2
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"{province_name}风险区划图",
                        subtitle=f"（{year}年）" if year else "（最新数据）",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=18,
                            font_weight="bold"
                        )
                    ),
                    visualmap_opts=opts.VisualMapOpts(
                        min_=0,
                        max_=5,
                        range_text=["极高风险", "极低风险"],
                        is_calculable=True,
                        range_color=["#e9ecef", "#d4edda", "#28a745", "#ffc107", "#fd7e14", "#dc3545"],
                        pos_left="left",
                        pos_bottom="20%",
                        pieces=[
                            {"min": 0, "max": 0, "label": "未评估", "color": "#e9ecef"},
                            {"min": 1, "max": 1, "label": "极低风险", "color": "#d4edda"},
                            {"min": 2, "max": 2, "label": "低风险", "color": "#28a745"},
                            {"min": 3, "max": 3, "label": "中风险", "color": "#ffc107"},
                            {"min": 4, "max": 4, "label": "高风险", "color": "#fd7e14"},
                            {"min": 5, "max": 5, "label": "极高风险", "color": "#dc3545"}
                        ]
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        trigger="item",
                        formatter="{b}: {c}"
                    ),
                    legend_opts=opts.LegendOpts(is_show=False)
                )
                .set_series_opts(
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        font_size=10
                    )
                )
            )
            
            # 生成文件名
            timestamp = int(time.time())
            year_suffix = f"_{year}" if year else "_latest"
            filename = f"{province_name.replace('省', '')}_risk_map{year_suffix}_{timestamp}.html"
            
            # 确保static/maps目录存在
            static_maps_dir = os.path.join(current_app.static_folder, 'maps')
            os.makedirs(static_maps_dir, exist_ok=True)
            
            # 生成文件路径
            file_path = os.path.join(static_maps_dir, filename)
            
            # 渲染地图
            map_chart.render(file_path)

            # 添加指北针和比例尺控件
            self.add_map_controls_to_html(file_path)

            # 返回相对于static的路径
            return f"maps/{filename}"
            
        except Exception as e:
            print(f"生成PyEcharts地图失败: {e}")
            return None

    def generate_china_overview_map(self, province_risk_data: List[Dict]) -> Optional[str]:
        """
        生成全国风险概览地图
        
        Args:
            province_risk_data: 省份风险数据列表
            
        Returns:
            地图文件的相对路径
        """
        try:
            from pyecharts.charts import Map
            from pyecharts import options as opts
            from pyecharts.globals import ThemeType
            from pyecharts.globals import CurrentConfig

            # 配置PyEcharts使用CDN资源
            CurrentConfig.ONLINE_HOST = "https://assets.pyecharts.org/assets/v5/"
            
            # 准备数据
            map_data = []
            for province_data in province_risk_data:
                province_name = province_data['province_name']
                avg_risk_index = province_data['avg_risk_index']
                
                # 转换为风险等级
                risk_level = self.get_risk_level_from_index(avg_risk_index)
                risk_value = self.risk_values.get(risk_level, 0)
                
                # 标准化省份名称
                map_name = self.province_map_names.get(province_name, province_name.replace('省', ''))
                
                map_data.append([map_name, risk_value])
            
            # 创建地图
            map_chart = (
                Map(init_opts=opts.InitOpts(
                    width="1000px", 
                    height="600px",
                    theme=ThemeType.WHITE
                ))
                .add(
                    series_name="平均风险等级",
                    data_pair=map_data,
                    maptype="china",
                    is_roam=True
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title="全国内涝风险概览图",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=20,
                            font_weight="bold"
                        )
                    ),
                    visualmap_opts=opts.VisualMapOpts(
                        min_=0,
                        max_=5,
                        range_text=["极高风险", "极低风险"],
                        is_calculable=True,
                        range_color=["#e9ecef", "#d4edda", "#28a745", "#ffc107", "#fd7e14", "#dc3545"],
                        pos_left="left",
                        pos_bottom="20%",
                        pieces=[
                            {"min": 0, "max": 0, "label": "未评估", "color": "#e9ecef"},
                            {"min": 1, "max": 1, "label": "极低风险", "color": "#d4edda"},
                            {"min": 2, "max": 2, "label": "低风险", "color": "#28a745"},
                            {"min": 3, "max": 3, "label": "中风险", "color": "#ffc107"},
                            {"min": 4, "max": 4, "label": "高风险", "color": "#fd7e14"},
                            {"min": 5, "max": 5, "label": "极高风险", "color": "#dc3545"}
                        ]
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        trigger="item",
                        formatter="{b}: {c}"
                    )
                )
                .set_series_opts(
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        font_size=8
                    )
                )
            )
            
            # 生成文件名
            timestamp = int(time.time())
            filename = f"china_overview_map_{timestamp}.html"
            
            # 确保static/maps目录存在
            static_maps_dir = os.path.join(current_app.static_folder, 'maps')
            os.makedirs(static_maps_dir, exist_ok=True)
            
            # 生成文件路径
            file_path = os.path.join(static_maps_dir, filename)
            
            # 渲染地图
            map_chart.render(file_path)

            # 添加指北针和比例尺控件
            self.add_map_controls_to_html(file_path)

            # 返回相对于static的路径
            return f"maps/{filename}"
            
        except Exception as e:
            print(f"生成全国概览地图失败: {e}")
            return None

    def generate_province_landuse_map(self, province_name: str, landuse_data: List[Dict], year: int = None) -> Optional[str]:
        """
        生成省份土地利用地图

        Args:
            province_name: 省份名称
            landuse_data: 土地利用数据列表，每个元素包含城市名称和土地利用类型
            year: 年份（可选）

        Returns:
            生成的HTML文件路径，失败返回None
        """
        try:
            # 检查PyEcharts是否可用
            try:
                from pyecharts import options as opts
                from pyecharts.charts import Map
                from pyecharts.globals import ThemeType, CurrentConfig

                # 配置PyEcharts使用CDN资源
                CurrentConfig.ONLINE_HOST = "https://assets.pyecharts.org/assets/v5/"
            except ImportError:
                print("PyEcharts不可用，无法生成地图")
                return None

            # 获取PyEcharts地图名称
            map_name = self.province_map_names.get(province_name, province_name.replace('省', '').replace('市', '').replace('自治区', '').replace('特别行政区', ''))

            print(f"生成土地利用地图: {province_name} -> {map_name}")
            print(f"土地利用数据: {len(landuse_data)} 条记录")

            # 处理土地利用数据
            city_landuse_mapping = {}
            landuse_stats = {}

            for data in landuse_data:
                city_name = data.get('city_name') or data.get('name') or data.get('location_name', '')
                landuse_type = data.get('landuse_type', '其他')

                # 清理城市名称
                city_name = city_name.replace('市', '').replace('县', '').replace('区', '')

                # 统计土地利用类型
                if landuse_type not in landuse_stats:
                    landuse_stats[landuse_type] = 0
                landuse_stats[landuse_type] += 1

                # 如果一个城市有多种土地利用类型，选择最主要的类型
                if city_name not in city_landuse_mapping:
                    city_landuse_mapping[city_name] = landuse_type
                else:
                    # 简单策略：保持第一个类型
                    pass

            print(f"城市土地利用映射: {len(city_landuse_mapping)} 个城市")
            print(f"土地利用类型统计: {landuse_stats}")

            # 构建地图数据
            map_data = []
            for city_name, landuse_type in city_landuse_mapping.items():
                value = self.landuse_values.get(landuse_type, 0)
                map_data.append([city_name, value])

            if not map_data:
                print("没有有效的地图数据")
                return None

            print(f"地图数据: {map_data[:5]}...")  # 显示前5条数据

            # 创建地图
            map_chart = (
                Map(init_opts=opts.InitOpts(
                    width="900px",
                    height="500px",
                    theme=ThemeType.WHITE
                ))
                .add(
                    series_name="土地利用类型",
                    data_pair=map_data,
                    maptype=map_name,
                    is_roam=True,
                    zoom=1.2
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"{province_name}土地利用图",
                        subtitle=f"数据年份: {year or '最新'}" if year else "基于上传数据生成",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(font_size=18, color="#333")
                    ),
                    legend_opts=opts.LegendOpts(
                        is_show=True,
                        pos_right="10px",
                        pos_top="50px",
                        orient="vertical"
                    ),
                    visualmap_opts=opts.VisualMapOpts(
                        is_show=True,
                        type_="piecewise",
                        pieces=[
                            {"min": 1, "max": 1, "label": "耕地", "color": self.landuse_colors['耕地']},
                            {"min": 2, "max": 2, "label": "林地", "color": self.landuse_colors['林地']},
                            {"min": 3, "max": 3, "label": "草地", "color": self.landuse_colors['草地']},
                            {"min": 4, "max": 4, "label": "水域", "color": self.landuse_colors['水域']},
                            {"min": 5, "max": 5, "label": "建设用地", "color": self.landuse_colors['建设用地']},
                            {"min": 6, "max": 6, "label": "未利用地", "color": self.landuse_colors['未利用地']},
                            {"min": 7, "max": 7, "label": "住宅用地", "color": self.landuse_colors['住宅用地']},
                            {"min": 8, "max": 8, "label": "商业用地", "color": self.landuse_colors['商业用地']},
                            {"min": 9, "max": 9, "label": "工业用地", "color": self.landuse_colors['工业用地']},
                            {"min": 10, "max": 10, "label": "交通用地", "color": self.landuse_colors['交通用地']},
                            {"min": 0, "max": 0, "label": "其他", "color": self.landuse_colors['其他']},
                        ],
                        pos_left="10px",
                        pos_bottom="50px"
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        trigger="item",
                        formatter="{b}: {c}"
                    )
                )
                .set_series_opts(
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        font_size=10,
                        color="#333"
                    )
                )
            )

            # 生成HTML文件
            year_suffix = f"_{year}" if year else ""
            filename = f"landuse_map_{province_name}{year_suffix}_{int(time.time())}.html"

            # 确保静态文件目录存在（使用Flask配置的static_folder）
            static_dir = os.path.join(current_app.static_folder, 'maps')
            os.makedirs(static_dir, exist_ok=True)

            file_path = os.path.join(static_dir, filename)
            map_chart.render(file_path)

            # 添加指北针和比例尺控件
            self.add_map_controls_to_html(file_path)

            print(f"土地利用地图已生成: {file_path}")
            return f"/static/maps/{filename}"

        except Exception as e:
            print(f"生成土地利用地图失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def generate_province_admin_map(self, province_name: str) -> Optional[str]:
        """
        生成省份市级行政区划地图

        Args:
            province_name: 省份名称

        Returns:
            生成的HTML文件路径
        """
        try:
            from pyecharts.charts import Map
            from pyecharts import options as opts
            from pyecharts.globals import ThemeType
            from pyecharts.globals import CurrentConfig

            # 配置PyEcharts使用CDN资源
            CurrentConfig.ONLINE_HOST = "https://assets.pyecharts.org/assets/v5/"

            # 标准化省份名称
            map_name = self.province_map_names.get(province_name, province_name.replace('省', ''))

            # 检查是否支持该省份
            if map_name not in self.province_cities:
                print(f"不支持的省份: {province_name} (标准化名称: {map_name})")
                return None

            # 获取该省份的城市列表
            cities = self.province_cities[map_name]

            # 准备地图数据 - 所有城市使用统一的值，确保统一颜色
            map_data = []
            for city in cities:
                # 使用固定值1，确保所有区域颜色一致
                map_data.append([city, 1])

            print(f"生成 {province_name} 行政区划地图，包含 {len(map_data)} 个城市")

            # 创建地图
            map_chart = (
                Map(init_opts=opts.InitOpts(
                    width="900px",
                    height="500px",
                    theme=ThemeType.WHITE
                ))
                .add(
                    series_name="行政区划",
                    data_pair=map_data,
                    maptype=map_name,
                    is_roam=True,
                    zoom=1.2
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"{province_name}行政区划图",
                        subtitle="市级行政区划边界",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=18,
                            font_weight="bold",
                            color="#2c3e50"
                        ),
                        subtitle_textstyle_opts=opts.TextStyleOpts(
                            font_size=12,
                            color="#7f8c8d"
                        )
                    ),
                    legend_opts=opts.LegendOpts(
                        is_show=False  # 隐藏图例，因为所有区域颜色相同
                    ),
                    visualmap_opts=opts.VisualMapOpts(
                        is_show=False,  # 隐藏视觉映射组件
                        min_=0,
                        max_=1,
                        range_color=["#a8e6cf", "#a8e6cf"],  # 统一的浅绿色
                        is_piecewise=False
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        trigger="item",
                        formatter="{b}<br/>行政级别: 地级市"
                    )
                )
                .set_series_opts(
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        font_size=10,
                        color="#2c3e50",
                        font_weight="bold"
                    ),
                    # 设置区域样式，确保边界线清晰
                    itemstyle_opts=opts.ItemStyleOpts(
                        color="#a8e6cf",  # 统一的浅绿色填充
                        border_color="#2c3e50",  # 深色边界线
                        border_width=2  # 边界线宽度
                    )
                )
            )

            # 生成文件名
            timestamp = int(time.time())
            filename = f"{province_name.replace('省', '')}_admin_map_{timestamp}.html"

            # 确保static/maps目录存在
            static_maps_dir = os.path.join(current_app.static_folder, 'maps')
            os.makedirs(static_maps_dir, exist_ok=True)

            # 生成文件路径
            file_path = os.path.join(static_maps_dir, filename)

            # 渲染地图
            map_chart.render(file_path)

            # 添加指北针和比例尺控件
            self.add_map_controls_to_html(file_path)

            print(f"省份行政区划地图生成成功: {file_path}")

            # 返回相对于static的路径
            return f"maps/{filename}"

        except Exception as e:
            print(f"生成省份行政区划地图失败: {e}")
            import traceback
            traceback.print_exc()
            return None


# 创建全局实例
pyecharts_map_generator = PyEchartsMapGenerator()
