<!DOCTYPE html>
<html>
<head>
<style>
/* 地图控件样式 - 指北针和比例尺 */
.map-control-compass {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    padding: 8px;
    transition: all 0.3s ease;
    cursor: default;
    user-select: none;
    z-index: 1000;
}

.map-control-compass:hover {
    background: rgba(255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
}

.compass-container {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.compass-needle {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.compass-north {
    font-size: 12px;
    font-weight: bold;
    color: #dc3545;
    margin-bottom: 2px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.compass-arrow {
    font-size: 20px;
    color: #dc3545;
    line-height: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.map-control-scale {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    padding: 6px 10px;
    transition: all 0.3s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    z-index: 1000;
}

.map-control-scale:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.scale-bar {
    border: 2px solid #333;
    border-top: none;
    color: #333;
    font-size: 11px;
    font-weight: 600;
    line-height: 1.2;
    padding: 2px 5px 1px;
    background: transparent;
    white-space: nowrap;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    margin-bottom: 4px;
    min-width: 60px;
    max-width: 200px;
    text-align: center;
    transition: width 0.3s ease;
    position: relative;
}

.scale-info {
    font-size: 10px;
    color: #666;
    text-align: center;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}
</style>

    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/maps/henan.js"></script>

    
</head>
<body >
    <div id="2eb5cb33e8da498098bc72c6e5c3584b" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_2eb5cb33e8da498098bc72c6e5c3584b = echarts.init(
            document.getElementById('2eb5cb33e8da498098bc72c6e5c3584b'), 'white', {renderer: 'canvas'});
        var option_2eb5cb33e8da498098bc72c6e5c3584b = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "map",
            "name": "\u98ce\u9669\u7b49\u7ea7",
            "label": {
                "show": true,
                "margin": 8,
                "fontSize": 10,
                "valueAnimation": false
            },
            "map": "\u6cb3\u5357",
            "data": [
                {
                    "name": "\u90d1\u5dde\u5e02",
                    "value": 5
                },
                {
                    "name": "\u5f00\u5c01\u5e02",
                    "value": 3
                },
                {
                    "name": "\u6d1b\u9633\u5e02",
                    "value": 3
                },
                {
                    "name": "\u5e73\u9876\u5c71\u5e02",
                    "value": 4
                },
                {
                    "name": "\u5b89\u9633\u5e02",
                    "value": 4
                },
                {
                    "name": "\u9e64\u58c1\u5e02",
                    "value": 4
                },
                {
                    "name": "\u65b0\u4e61\u5e02",
                    "value": 3
                },
                {
                    "name": "\u7126\u4f5c\u5e02",
                    "value": 3
                },
                {
                    "name": "\u6fee\u9633\u5e02",
                    "value": 2
                },
                {
                    "name": "\u8bb8\u660c\u5e02",
                    "value": 4
                },
                {
                    "name": "\u6f2f\u6cb3\u5e02",
                    "value": 4
                },
                {
                    "name": "\u4e09\u95e8\u5ce1\u5e02",
                    "value": 2
                },
                {
                    "name": "\u5357\u9633\u5e02",
                    "value": 3
                },
                {
                    "name": "\u5546\u4e18\u5e02",
                    "value": 4
                },
                {
                    "name": "\u4fe1\u9633\u5e02",
                    "value": 4
                },
                {
                    "name": "\u5468\u53e3\u5e02",
                    "value": 4
                },
                {
                    "name": "\u9a7b\u9a6c\u5e97\u5e02",
                    "value": 4
                },
                {
                    "name": "\u6d4e\u6e90\u5e02",
                    "value": 2
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1.2,
            "zlevel": 0,
            "z": 2,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "mapValueCalculation": "sum",
            "showLegendSymbol": true,
            "emphasis": {},
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u98ce\u9669\u7b49\u7ea7"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": "{b}: {c}",
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u6cb3\u5357\u7701\u98ce\u9669\u533a\u5212\u56fe",
            "target": "blank",
            "subtext": "\uff082023\u5e74\uff09",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "fontWeight": "bold",
                "fontSize": 18
            }
        }
    ],
    "visualMap": {
        "show": true,
        "type": "continuous",
        "min": 0,
        "max": 5,
        "text": [
            "\u6781\u9ad8\u98ce\u9669",
            "\u6781\u4f4e\u98ce\u9669"
        ],
        "inRange": {
            "color": [
                "#e9ecef",
                "#d4edda",
                "#28a745",
                "#ffc107",
                "#fd7e14",
                "#dc3545"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "hoverLink": true,
        "orient": "vertical",
        "left": "left",
        "bottom": "20%",
        "padding": 5,
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 140,
        "borderWidth": 0
    }
};
        chart_2eb5cb33e8da498098bc72c6e5c3584b.setOption(option_2eb5cb33e8da498098bc72c6e5c3584b);
    </script>

<div class="map-control-compass" title="指北针 - 北向指示器">
    <div class="compass-container">
        <div class="compass-needle">
            <div class="compass-north">N</div>
            <div class="compass-arrow">↑</div>
        </div>
    </div>
</div>

<div class="map-control-scale" title="比例尺">
    <div class="scale-bar" id="scaleBar">1000 km</div>
    <div class="scale-info" id="scaleInfo">缩放级别: 1</div>
</div>

<script>
// 全局变量跟踪缩放状态
window.currentMapZoom = 1;

// 动态比例尺功能 - 采用土地利用图的简化方法
function updateScale() {
    try {
        var scaleBar = document.getElementById('scaleBar');
        var scaleInfo = document.getElementById('scaleInfo');

        if (!scaleBar || !scaleInfo) return;

        // 获取ECharts实例
        var chartDom = document.getElementById('main');
        if (!chartDom) return;

        var myChart = echarts.getInstanceByDom(chartDom);
        if (!myChart) return;

        // 获取当前缩放级别和地图配置
        var option = myChart.getOption();
        var geo = option.geo && option.geo[0];
        if (!geo) return;

        var zoom = geo.zoom || 1;
        var mapType = geo.map || 'china';

        // 如果缩放没有变化，跳过更新
        if (Math.abs(zoom - window.currentMapZoom) < 0.01) {
            return;
        }

        console.log('更新比例尺 - 地图类型:', mapType, '缩放级别:', zoom);
        window.currentMapZoom = zoom;

        // 使用简化的比例尺计算方法
        var scaleData = calculateSimpleScale(mapType, zoom, chartDom);

        scaleBar.textContent = scaleData.scaleText;
        scaleInfo.textContent = scaleData.infoText;

        // 动态调整比例尺条的宽度
        var scaleWidth = Math.max(60, Math.min(150, scaleData.pixelWidth));
        scaleBar.style.width = scaleWidth + 'px';

        console.log('比例尺已更新:', scaleData.scaleText, '信息:', scaleData.infoText);

    } catch (error) {
        console.log('更新比例尺时出错:', error);
    }
}

// 简化的比例尺计算方法 - 采用土地利用图的方法
function calculateSimpleScale(mapType, zoom, chartDom) {
    try {
        // 获取图表容器的像素尺寸
        var containerWidth = chartDom.offsetWidth;
        var containerHeight = chartDom.offsetHeight;

        // 不同地图类型的地理范围（经纬度）
        var mapBounds = getMapBounds(mapType);
        if (!mapBounds) {
            return {
                scaleText: '100 km',
                infoText: '1:10000000 (缩放: 1.0x)',
                pixelWidth: 100
            };
        }

        // 计算地理宽度（经度差转换为公里）
        var geoWidth = Math.abs(mapBounds.east - mapBounds.west);
        var geoHeight = Math.abs(mapBounds.north - mapBounds.south);

        // 考虑纬度影响，使用平均纬度
        var avgLat = (mapBounds.north + mapBounds.south) / 2;
        var latFactor = Math.cos(avgLat * Math.PI / 180);

        var realWidthKm = geoWidth * 111 * latFactor;
        var realHeightKm = geoHeight * 111;

        // 使用较小的维度来计算比例尺，确保准确性
        var realDistanceKm = Math.min(realWidthKm, realHeightKm);
        var pixelDistance = Math.min(containerWidth, containerHeight);

        // 计算每像素代表的公里数，考虑缩放级别
        var kmPerPixel = realDistanceKm / (pixelDistance * zoom);

        // 生成合适的比例尺长度（100像素）
        var scalePixels = 100;
        var scaleKm = kmPerPixel * scalePixels;

        // 调整到合适的数值
        var displayKm;
        var scaleText;

        if (scaleKm >= 1000) {
            displayKm = Math.round(scaleKm / 1000) * 1000;
            scaleText = (displayKm / 1000) + '000 km';
        } else if (scaleKm >= 100) {
            displayKm = Math.round(scaleKm / 100) * 100;
            scaleText = displayKm + ' km';
        } else if (scaleKm >= 10) {
            displayKm = Math.round(scaleKm / 10) * 10;
            scaleText = displayKm + ' km';
        } else if (scaleKm >= 1) {
            displayKm = Math.round(scaleKm);
            scaleText = displayKm + ' km';
        } else {
            displayKm = scaleKm;
            scaleText = Math.round(displayKm * 1000) + ' m';
        }

        var infoText = '1:' + Math.round(displayKm * 100000) + ' (缩放: ' + zoom.toFixed(1) + 'x)';

        return {
            scaleText: scaleText,
            infoText: infoText,
            pixelWidth: scalePixels
        };

    } catch (error) {
        console.log('计算比例尺时出错:', error);
        return {
            scaleText: '100 km',
            infoText: '1:10000000 (缩放: 1.0x)',
            pixelWidth: 100
        };
    }
}

// 获取不同地图类型的地理边界
function getMapBounds(mapType) {
    var bounds = {
        'china': { north: 53.5, south: 18.2, east: 134.8, west: 73.5 },
        '河南': { north: 36.4, south: 31.4, east: 116.6, west: 110.4 },
        '北京': { north: 41.1, south: 39.4, east: 117.5, west: 115.4 },
        '上海': { north: 31.9, south: 30.7, east: 122.2, west: 120.9 },
        '广东': { north: 25.3, south: 20.2, east: 117.2, west: 109.7 },
        '江苏': { north: 35.1, south: 30.8, east: 121.9, west: 116.4 },
        '浙江': { north: 31.2, south: 27.0, east: 123.2, west: 118.0 },
        '山东': { north: 38.4, south: 34.4, east: 122.7, west: 114.8 },
        '四川': { north: 34.3, south: 26.0, east: 108.5, west: 97.4 },
        '湖北': { north: 33.3, south: 29.0, east: 116.1, west: 108.3 },
        '湖南': { north: 30.1, south: 24.6, east: 114.3, west: 108.8 },
        '福建': { north: 28.3, south: 23.5, east: 120.7, west: 115.8 },
        '安徽': { north: 34.7, south: 29.4, east: 119.3, west: 114.9 },
        '江西': { north: 30.0, south: 24.5, east: 118.5, west: 113.6 },
        '陕西': { north: 39.6, south: 31.7, east: 111.3, west: 105.5 }
    };

    // 移除省字后缀进行匹配
    var normalizedMapType = mapType.replace('省', '');
    return bounds[normalizedMapType] || bounds[mapType] || bounds['china'];
}

// 格式化比例尺数值
function formatScaleValue(km) {
    if (km >= 1000) {
        var thousands = Math.round(km / 1000);
        if (thousands >= 10) {
            thousands = Math.round(thousands / 10) * 10;
        }
        return { value: thousands * 1000, text: thousands + '000 km' };
    } else if (km >= 100) {
        var hundreds = Math.round(km / 100) * 100;
        return { value: hundreds, text: hundreds + ' km' };
    } else if (km >= 10) {
        var tens = Math.round(km / 10) * 10;
        return { value: tens, text: tens + ' km' };
    } else if (km >= 1) {
        var ones = Math.round(km);
        return { value: ones, text: ones + ' km' };
    } else {
        var meters = Math.round(km * 1000);
        if (meters >= 100) {
            meters = Math.round(meters / 100) * 100;
        } else if (meters >= 10) {
            meters = Math.round(meters / 10) * 10;
        }
        return { value: meters / 1000, text: meters + ' m' };
    }
}

// 监听地图缩放事件
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保ECharts完全加载
    setTimeout(function() {
        initializeScaleControls();
    }, 1500);

    // 如果页面已经加载完成，也尝试初始化
    if (document.readyState === 'complete') {
        setTimeout(initializeScaleControls, 500);
    }
});

// 初始化比例尺控件
function initializeScaleControls() {
    try {
        var chartDom = document.getElementById('main');
        if (!chartDom) {
            console.log('地图容器未找到，稍后重试...');
            setTimeout(initializeScaleControls, 1000);
            return;
        }

        var myChart = echarts.getInstanceByDom(chartDom);
        if (!myChart) {
            console.log('ECharts实例未找到，稍后重试...');
            setTimeout(initializeScaleControls, 1000);
            return;
        }

        console.log('开始初始化地图比例尺控件...');

        // 监听地图漫游事件（包括缩放和平移）
        myChart.on('georoam', function(params) {
            console.log('地图漫游事件:', params);
            // 立即更新一次
            updateScale();
            // 延迟更新确保状态同步
            setTimeout(updateScale, 100);
        });

        // 监听地图完成事件
        myChart.on('finished', function() {
            console.log('地图渲染完成，更新比例尺');
            setTimeout(updateScale, 200);
        });

        // 监听鼠标滚轮事件
        chartDom.addEventListener('wheel', function(e) {
            console.log('鼠标滚轮事件');
            setTimeout(updateScale, 100);
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            console.log('窗口大小变化，更新比例尺');
            setTimeout(updateScale, 300);
        });

        // 初始化比例尺显示
        updateScale();
        setTimeout(updateScale, 500);
        setTimeout(updateScale, 1000);

        // 定期更新比例尺（备用机制）
        setInterval(updateScale, 2000);

        console.log('比例尺控件初始化完成');

    } catch (error) {
        console.log('初始化比例尺监听器时出错:', error);
        // 如果初始化失败，稍后重试
        setTimeout(initializeScaleControls, 2000);
    }
}

// 手动触发比例尺更新（供外部调用）
window.updateMapScale = function() {
    updateScale();
};
</script>
</body>
</html>
